"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/layout",{

/***/ "(app-pages-browser)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: function() { return /* binding */ ScrollArea; },\n/* harmony export */   ScrollBar: function() { return /* binding */ ScrollBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-scroll-area_951b2c98b871c6cc4614653d3a853dcc/node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n\n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden bg-card phablet:bg-muted/0\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 15,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 18,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 19,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = ScrollArea;\nScrollArea.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, orientation = \"vertical\", ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 28,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = ScrollBar;\nScrollBar.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar.displayName;\n\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ScrollArea$React.forwardRef\");\n$RefreshReg$(_c1, \"ScrollArea\");\n$RefreshReg$(_c2, \"ScrollBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/scroll-area.tsx\n"));

/***/ })

});