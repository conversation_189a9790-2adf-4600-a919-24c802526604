"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15";
exports.ids = ["vendor-chunks/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/list/index.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/list/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var _fullcalendar_core_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fullcalendar/core/index.js */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+core@6.1.15/node_modules/@fullcalendar/core/index.js\");\n/* harmony import */ var _internal_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./internal.js */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/list/internal.js\");\n/* harmony import */ var _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fullcalendar/core/internal.js */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+core@6.1.15/node_modules/@fullcalendar/core/internal-common.js\");\n\n\n\n\n\nconst OPTION_REFINERS = {\n    listDayFormat: createFalsableFormatter,\n    listDaySideFormat: createFalsableFormatter,\n    noEventsClassNames: _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.n,\n    noEventsContent: _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.n,\n    noEventsDidMount: _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.n,\n    noEventsWillUnmount: _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.n,\n    // noEventsText is defined in base options\n};\nfunction createFalsableFormatter(input) {\n    return input === false ? null : (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.x)(input);\n}\n\nvar index = (0,_fullcalendar_core_index_js__WEBPACK_IMPORTED_MODULE_1__.createPlugin)({\n    name: '@fullcalendar/list',\n    optionRefiners: OPTION_REFINERS,\n    views: {\n        list: {\n            component: _internal_js__WEBPACK_IMPORTED_MODULE_2__.ListView,\n            buttonTextKey: 'list',\n            listDayFormat: { month: 'long', day: 'numeric', year: 'numeric' }, // like \"January 1, 2016\"\n        },\n        listDay: {\n            type: 'list',\n            duration: { days: 1 },\n            listDayFormat: { weekday: 'long' }, // day-of-week is all we need. full date is probably in headerToolbar\n        },\n        listWeek: {\n            type: 'list',\n            duration: { weeks: 1 },\n            listDayFormat: { weekday: 'long' },\n            listDaySideFormat: { month: 'long', day: 'numeric', year: 'numeric' },\n        },\n        listMonth: {\n            type: 'list',\n            duration: { month: 1 },\n            listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n        },\n        listYear: {\n            type: 'list',\n            duration: { year: 1 },\n            listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n        },\n    },\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/list/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/list/internal.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/list/internal.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListView: () => (/* binding */ ListView)\n/* harmony export */ });\n/* harmony import */ var _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fullcalendar/core/internal.js */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+core@6.1.15/node_modules/@fullcalendar/core/internal-common.js\");\n/* harmony import */ var _fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fullcalendar/core/preact.js */ \"(ssr)/./node_modules/.pnpm/preact@10.12.1/node_modules/preact/dist/preact.mjs\");\n\n\n\nclass ListViewHeaderRow extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.B {\n    constructor() {\n        super(...arguments);\n        this.state = {\n            textId: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.a5)(),\n        };\n    }\n    render() {\n        let { theme, dateEnv, options, viewApi } = this.context;\n        let { cellId, dayDate, todayRange } = this.props;\n        let { textId } = this.state;\n        let dayMeta = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.a_)(dayDate, todayRange);\n        // will ever be falsy?\n        let text = options.listDayFormat ? dateEnv.format(dayDate, options.listDayFormat) : '';\n        // will ever be falsy? also, BAD NAME \"alt\"\n        let sideText = options.listDaySideFormat ? dateEnv.format(dayDate, options.listDaySideFormat) : '';\n        let renderProps = Object.assign({ date: dateEnv.toDate(dayDate), view: viewApi, textId,\n            text,\n            sideText, navLinkAttrs: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.b0)(this.context, dayDate), sideNavLinkAttrs: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.b0)(this.context, dayDate, 'day', false) }, dayMeta);\n        // TODO: make a reusable HOC for dayHeader (used in daygrid/timegrid too)\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.C, { elTag: \"tr\", elClasses: [\n                'fc-list-day',\n                ...(0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.aZ)(dayMeta, theme),\n            ], elAttrs: {\n                'data-date': (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bv)(dayDate),\n            }, renderProps: renderProps, generatorName: \"dayHeaderContent\", customGenerator: options.dayHeaderContent, defaultGenerator: renderInnerContent, classNameGenerator: options.dayHeaderClassNames, didMount: options.dayHeaderDidMount, willUnmount: options.dayHeaderWillUnmount }, (InnerContent) => ( // TODO: force-hide top border based on :first-child\n        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"th\", { scope: \"colgroup\", colSpan: 3, id: cellId, \"aria-labelledby\": textId },\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(InnerContent, { elTag: \"div\", elClasses: [\n                    'fc-list-day-cushion',\n                    theme.getClass('tableCellShaded'),\n                ] })))));\n    }\n}\nfunction renderInnerContent(props) {\n    return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null,\n        props.text && ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"a\", Object.assign({ id: props.textId, className: \"fc-list-day-text\" }, props.navLinkAttrs), props.text)),\n        props.sideText && ( /* not keyboard tabbable */(0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"a\", Object.assign({ \"aria-hidden\": true, className: \"fc-list-day-side-text\" }, props.sideNavLinkAttrs), props.sideText))));\n}\n\nconst DEFAULT_TIME_FORMAT = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.x)({\n    hour: 'numeric',\n    minute: '2-digit',\n    meridiem: 'short',\n});\nclass ListViewEventRow extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.B {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { seg, timeHeaderId, eventHeaderId, dateHeaderId } = props;\n        let timeFormat = options.eventTimeFormat || DEFAULT_TIME_FORMAT;\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cn, Object.assign({}, props, { elTag: \"tr\", elClasses: [\n                'fc-list-event',\n                seg.eventRange.def.url && 'fc-event-forced-url',\n            ], defaultGenerator: () => renderEventInnerContent(seg, context) /* weird */, seg: seg, timeText: \"\", disableDragging: true, disableResizing: true }), (InnerContent, eventContentArg) => ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null,\n            buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId),\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"td\", { \"aria-hidden\": true, className: \"fc-list-event-graphic\" },\n                (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"span\", { className: \"fc-list-event-dot\", style: {\n                        borderColor: eventContentArg.borderColor || eventContentArg.backgroundColor,\n                    } })),\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(InnerContent, { elTag: \"td\", elClasses: ['fc-list-event-title'], elAttrs: { headers: `${eventHeaderId} ${dateHeaderId}` } })))));\n    }\n}\nfunction renderEventInnerContent(seg, context) {\n    let interactiveAttrs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bU)(seg, context);\n    return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"a\", Object.assign({}, interactiveAttrs), seg.eventRange.def.title));\n}\nfunction buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId) {\n    let { options } = context;\n    if (options.displayEventTime !== false) {\n        let eventDef = seg.eventRange.def;\n        let eventInstance = seg.eventRange.instance;\n        let doAllDay = false;\n        let timeText;\n        if (eventDef.allDay) {\n            doAllDay = true;\n        }\n        else if ((0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.az)(seg.eventRange.range)) { // TODO: use (!isStart || !isEnd) instead?\n            if (seg.isStart) {\n                timeText = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bQ)(seg, timeFormat, context, null, null, eventInstance.range.start, seg.end);\n            }\n            else if (seg.isEnd) {\n                timeText = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bQ)(seg, timeFormat, context, null, null, seg.start, eventInstance.range.end);\n            }\n            else {\n                doAllDay = true;\n            }\n        }\n        else {\n            timeText = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bQ)(seg, timeFormat, context);\n        }\n        if (doAllDay) {\n            let renderProps = {\n                text: context.options.allDayText,\n                view: context.viewApi,\n            };\n            return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.C, { elTag: \"td\", elClasses: ['fc-list-event-time'], elAttrs: {\n                    headers: `${timeHeaderId} ${dateHeaderId}`,\n                }, renderProps: renderProps, generatorName: \"allDayContent\", customGenerator: options.allDayContent, defaultGenerator: renderAllDayInner, classNameGenerator: options.allDayClassNames, didMount: options.allDayDidMount, willUnmount: options.allDayWillUnmount }));\n        }\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"td\", { className: \"fc-list-event-time\" }, timeText));\n    }\n    return null;\n}\nfunction renderAllDayInner(renderProps) {\n    return renderProps.text;\n}\n\n/*\nResponsible for the scroller, and forwarding event-related actions into the \"grid\".\n*/\nclass ListView extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.be {\n    constructor() {\n        super(...arguments);\n        this.computeDateVars = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(computeDateVars);\n        this.eventStoreToSegs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(this._eventStoreToSegs);\n        this.state = {\n            timeHeaderId: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.a5)(),\n            eventHeaderId: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.a5)(),\n            dateHeaderIdRoot: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.a5)(),\n        };\n        this.setRootEl = (rootEl) => {\n            if (rootEl) {\n                this.context.registerInteractiveComponent(this, {\n                    el: rootEl,\n                });\n            }\n            else {\n                this.context.unregisterInteractiveComponent(this);\n            }\n        };\n    }\n    render() {\n        let { props, context } = this;\n        let { dayDates, dayRanges } = this.computeDateVars(props.dateProfile);\n        let eventSegs = this.eventStoreToSegs(props.eventStore, props.eventUiBases, dayRanges);\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ct, { elRef: this.setRootEl, elClasses: [\n                'fc-list',\n                context.theme.getClass('table'),\n                context.options.stickyHeaderDates !== false ?\n                    'fc-list-sticky' :\n                    '',\n            ], viewSpec: context.viewSpec },\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cd, { liquid: !props.isHeightAuto, overflowX: props.isHeightAuto ? 'visible' : 'hidden', overflowY: props.isHeightAuto ? 'visible' : 'auto' }, eventSegs.length > 0 ?\n                this.renderSegList(eventSegs, dayDates) :\n                this.renderEmptyMessage())));\n    }\n    renderEmptyMessage() {\n        let { options, viewApi } = this.context;\n        let renderProps = {\n            text: options.noEventsText,\n            view: viewApi,\n        };\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.C, { elTag: \"div\", elClasses: ['fc-list-empty'], renderProps: renderProps, generatorName: \"noEventsContent\", customGenerator: options.noEventsContent, defaultGenerator: renderNoEventsInner, classNameGenerator: options.noEventsClassNames, didMount: options.noEventsDidMount, willUnmount: options.noEventsWillUnmount }, (InnerContent) => ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(InnerContent, { elTag: \"div\", elClasses: ['fc-list-empty-cushion'] }))));\n    }\n    renderSegList(allSegs, dayDates) {\n        let { theme, options } = this.context;\n        let { timeHeaderId, eventHeaderId, dateHeaderIdRoot } = this.state;\n        let segsByDay = groupSegsByDay(allSegs); // sparse array\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ch, { unit: \"day\" }, (nowDate, todayRange) => {\n            let innerNodes = [];\n            for (let dayIndex = 0; dayIndex < segsByDay.length; dayIndex += 1) {\n                let daySegs = segsByDay[dayIndex];\n                if (daySegs) { // sparse array, so might be undefined\n                    let dayStr = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bv)(dayDates[dayIndex]);\n                    let dateHeaderId = dateHeaderIdRoot + '-' + dayStr;\n                    // append a day header\n                    innerNodes.push((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(ListViewHeaderRow, { key: dayStr, cellId: dateHeaderId, dayDate: dayDates[dayIndex], todayRange: todayRange }));\n                    daySegs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bR)(daySegs, options.eventOrder);\n                    for (let seg of daySegs) {\n                        innerNodes.push((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(ListViewEventRow, Object.assign({ key: dayStr + ':' + seg.eventRange.instance.instanceId /* are multiple segs for an instanceId */, seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: false, timeHeaderId: timeHeaderId, eventHeaderId: eventHeaderId, dateHeaderId: dateHeaderId }, (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bS)(seg, todayRange, nowDate))));\n                    }\n                }\n            }\n            return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"table\", { className: 'fc-list-table ' + theme.getClass('table') },\n                (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"thead\", null,\n                    (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"tr\", null,\n                        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"th\", { scope: \"col\", id: timeHeaderId }, options.timeHint),\n                        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"th\", { scope: \"col\", \"aria-hidden\": true }),\n                        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"th\", { scope: \"col\", id: eventHeaderId }, options.eventHint))),\n                (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"tbody\", null, innerNodes)));\n        }));\n    }\n    _eventStoreToSegs(eventStore, eventUiBases, dayRanges) {\n        return this.eventRangesToSegs((0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.af)(eventStore, eventUiBases, this.props.dateProfile.activeRange, this.context.options.nextDayThreshold).fg, dayRanges);\n    }\n    eventRangesToSegs(eventRanges, dayRanges) {\n        let segs = [];\n        for (let eventRange of eventRanges) {\n            segs.push(...this.eventRangeToSegs(eventRange, dayRanges));\n        }\n        return segs;\n    }\n    eventRangeToSegs(eventRange, dayRanges) {\n        let { dateEnv } = this.context;\n        let { nextDayThreshold } = this.context.options;\n        let range = eventRange.range;\n        let allDay = eventRange.def.allDay;\n        let dayIndex;\n        let segRange;\n        let seg;\n        let segs = [];\n        for (dayIndex = 0; dayIndex < dayRanges.length; dayIndex += 1) {\n            segRange = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.o)(range, dayRanges[dayIndex]);\n            if (segRange) {\n                seg = {\n                    component: this,\n                    eventRange,\n                    start: segRange.start,\n                    end: segRange.end,\n                    isStart: eventRange.isStart && segRange.start.valueOf() === range.start.valueOf(),\n                    isEnd: eventRange.isEnd && segRange.end.valueOf() === range.end.valueOf(),\n                    dayIndex,\n                };\n                segs.push(seg);\n                // detect when range won't go fully into the next day,\n                // and mutate the latest seg to the be the end.\n                if (!seg.isEnd && !allDay &&\n                    dayIndex + 1 < dayRanges.length &&\n                    range.end <\n                        dateEnv.add(dayRanges[dayIndex + 1].start, nextDayThreshold)) {\n                    seg.end = range.end;\n                    seg.isEnd = true;\n                    break;\n                }\n            }\n        }\n        return segs;\n    }\n}\nfunction renderNoEventsInner(renderProps) {\n    return renderProps.text;\n}\nfunction computeDateVars(dateProfile) {\n    let dayStart = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.q)(dateProfile.renderRange.start);\n    let viewEnd = dateProfile.renderRange.end;\n    let dayDates = [];\n    let dayRanges = [];\n    while (dayStart < viewEnd) {\n        dayDates.push(dayStart);\n        dayRanges.push({\n            start: dayStart,\n            end: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.t)(dayStart, 1),\n        });\n        dayStart = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.t)(dayStart, 1);\n    }\n    return { dayDates, dayRanges };\n}\n// Returns a sparse array of arrays, segs grouped by their dayIndex\nfunction groupSegsByDay(segs) {\n    let segsByDay = []; // sparse array\n    let i;\n    let seg;\n    for (i = 0; i < segs.length; i += 1) {\n        seg = segs[i];\n        (segsByDay[seg.dayIndex] || (segsByDay[seg.dayIndex] = []))\n            .push(seg);\n    }\n    return segsByDay;\n}\n\nvar css_248z = \":root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:\\\"\\\";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}\";\n(0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cw)(css_248z);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@fullcalendar+list@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/list/internal.js\n");

/***/ })

};
;