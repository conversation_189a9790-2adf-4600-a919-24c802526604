"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15";
exports.ids = ["vendor-chunks/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/daygrid/index.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/daygrid/index.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var _fullcalendar_core_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fullcalendar/core/index.js */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+core@6.1.15/node_modules/@fullcalendar/core/index.js\");\n/* harmony import */ var _internal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internal.js */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/daygrid/internal.js\");\n\n\n\n\n\nvar index = (0,_fullcalendar_core_index_js__WEBPACK_IMPORTED_MODULE_0__.createPlugin)({\n    name: '@fullcalendar/daygrid',\n    initialView: 'dayGridMonth',\n    views: {\n        dayGrid: {\n            component: _internal_js__WEBPACK_IMPORTED_MODULE_1__.DayGridView,\n            dateProfileGeneratorClass: _internal_js__WEBPACK_IMPORTED_MODULE_1__.TableDateProfileGenerator,\n        },\n        dayGridDay: {\n            type: 'dayGrid',\n            duration: { days: 1 },\n        },\n        dayGridWeek: {\n            type: 'dayGrid',\n            duration: { weeks: 1 },\n        },\n        dayGridMonth: {\n            type: 'dayGrid',\n            duration: { months: 1 },\n            fixedWeekCount: true,\n        },\n        dayGridYear: {\n            type: 'dayGrid',\n            duration: { years: 1 },\n        },\n    },\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/daygrid/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/daygrid/internal.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/daygrid/internal.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayGridView: () => (/* binding */ DayTableView),\n/* harmony export */   DayTable: () => (/* binding */ DayTable),\n/* harmony export */   DayTableSlicer: () => (/* binding */ DayTableSlicer),\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   TableDateProfileGenerator: () => (/* binding */ TableDateProfileGenerator),\n/* harmony export */   TableRows: () => (/* binding */ TableRows),\n/* harmony export */   TableView: () => (/* binding */ TableView),\n/* harmony export */   buildDayTableModel: () => (/* binding */ buildDayTableModel),\n/* harmony export */   buildDayTableRenderRange: () => (/* binding */ buildDayTableRenderRange)\n/* harmony export */ });\n/* harmony import */ var _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fullcalendar/core/internal.js */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+core@6.1.15/node_modules/@fullcalendar/core/internal-common.js\");\n/* harmony import */ var _fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fullcalendar/core/preact.js */ \"(ssr)/./node_modules/.pnpm/preact@10.12.1/node_modules/preact/dist/preact.mjs\");\n\n\n\n/* An abstract class for the daygrid views, as well as month view. Renders one or more rows of day cells.\n----------------------------------------------------------------------------------------------------------------------*/\n// It is a manager for a Table subcomponent, which does most of the heavy lifting.\n// It is responsible for managing width/height.\nclass TableView extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.be {\n    constructor() {\n        super(...arguments);\n        this.headerElRef = (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createRef)();\n    }\n    renderSimpleLayout(headerRowContent, bodyContent) {\n        let { props, context } = this;\n        let sections = [];\n        let stickyHeaderDates = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cc)(context.options);\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunk: {\n                    elRef: this.headerElRef,\n                    tableClassName: 'fc-col-header',\n                    rowContent: headerRowContent,\n                },\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            chunk: { content: bodyContent },\n        });\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ct, { elClasses: ['fc-daygrid'], viewSpec: context.viewSpec },\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.b$, { liquid: !props.isHeightAuto && !props.forPrint, collapsibleWidth: props.forPrint, cols: [] /* TODO: make optional? */, sections: sections })));\n    }\n    renderHScrollLayout(headerRowContent, bodyContent, colCnt, dayMinWidth) {\n        let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n        if (!ScrollGrid) {\n            throw new Error('No ScrollGrid implementation');\n        }\n        let { props, context } = this;\n        let stickyHeaderDates = !props.forPrint && (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cc)(context.options);\n        let stickyFooterScrollbar = !props.forPrint && (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cb)(context.options);\n        let sections = [];\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunks: [{\n                        key: 'main',\n                        elRef: this.headerElRef,\n                        tableClassName: 'fc-col-header',\n                        rowContent: headerRowContent,\n                    }],\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            chunks: [{\n                    key: 'main',\n                    content: bodyContent,\n                }],\n        });\n        if (stickyFooterScrollbar) {\n            sections.push({\n                type: 'footer',\n                key: 'footer',\n                isSticky: true,\n                chunks: [{\n                        key: 'main',\n                        content: _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ca,\n                    }],\n            });\n        }\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ct, { elClasses: ['fc-daygrid'], viewSpec: context.viewSpec },\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(ScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, forPrint: props.forPrint, collapsibleWidth: props.forPrint, colGroups: [{ cols: [{ span: colCnt, minWidth: dayMinWidth }] }], sections: sections })));\n    }\n}\n\nfunction splitSegsByRow(segs, rowCnt) {\n    let byRow = [];\n    for (let i = 0; i < rowCnt; i += 1) {\n        byRow[i] = [];\n    }\n    for (let seg of segs) {\n        byRow[seg.row].push(seg);\n    }\n    return byRow;\n}\nfunction splitSegsByFirstCol(segs, colCnt) {\n    let byCol = [];\n    for (let i = 0; i < colCnt; i += 1) {\n        byCol[i] = [];\n    }\n    for (let seg of segs) {\n        byCol[seg.firstCol].push(seg);\n    }\n    return byCol;\n}\nfunction splitInteractionByRow(ui, rowCnt) {\n    let byRow = [];\n    if (!ui) {\n        for (let i = 0; i < rowCnt; i += 1) {\n            byRow[i] = null;\n        }\n    }\n    else {\n        for (let i = 0; i < rowCnt; i += 1) {\n            byRow[i] = {\n                affectedInstances: ui.affectedInstances,\n                isEvent: ui.isEvent,\n                segs: [],\n            };\n        }\n        for (let seg of ui.segs) {\n            byRow[seg.row].segs.push(seg);\n        }\n    }\n    return byRow;\n}\n\nconst DEFAULT_TABLE_EVENT_TIME_FORMAT = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.x)({\n    hour: 'numeric',\n    minute: '2-digit',\n    omitZeroMinute: true,\n    meridiem: 'narrow',\n});\nfunction hasListItemDisplay(seg) {\n    let { display } = seg.eventRange.ui;\n    return display === 'list-item' || (display === 'auto' &&\n        !seg.eventRange.def.allDay &&\n        seg.firstCol === seg.lastCol && // can't be multi-day\n        seg.isStart && // \"\n        seg.isEnd // \"\n    );\n}\n\nclass TableBlockEvent extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.B {\n    render() {\n        let { props } = this;\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cj, Object.assign({}, props, { elClasses: ['fc-daygrid-event', 'fc-daygrid-block-event', 'fc-h-event'], defaultTimeFormat: DEFAULT_TABLE_EVENT_TIME_FORMAT, defaultDisplayEventEnd: props.defaultDisplayEventEnd, disableResizing: !props.seg.eventRange.def.allDay })));\n    }\n}\n\nclass TableListItemEvent extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.B {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { seg } = props;\n        let timeFormat = options.eventTimeFormat || DEFAULT_TABLE_EVENT_TIME_FORMAT;\n        let timeText = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bQ)(seg, timeFormat, context, true, props.defaultDisplayEventEnd);\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cn, Object.assign({}, props, { elTag: \"a\", elClasses: ['fc-daygrid-event', 'fc-daygrid-dot-event'], elAttrs: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bU)(props.seg, context), defaultGenerator: renderInnerContent, timeText: timeText, isResizing: false, isDateSelecting: false })));\n    }\n}\nfunction renderInnerContent(renderProps) {\n    return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null,\n        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-daygrid-event-dot\", style: { borderColor: renderProps.borderColor || renderProps.backgroundColor } }),\n        renderProps.timeText && ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-event-time\" }, renderProps.timeText)),\n        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-event-title\" }, renderProps.event.title || (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, \"\\u00A0\"))));\n}\n\nclass TableCellMoreLink extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.B {\n    constructor() {\n        super(...arguments);\n        this.compileSegs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(compileSegs);\n    }\n    render() {\n        let { props } = this;\n        let { allSegs, invisibleSegs } = this.compileSegs(props.singlePlacements);\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cr, { elClasses: ['fc-daygrid-more-link'], dateProfile: props.dateProfile, todayRange: props.todayRange, allDayDate: props.allDayDate, moreCnt: props.moreCnt, allSegs: allSegs, hiddenSegs: invisibleSegs, alignmentElRef: props.alignmentElRef, alignGridTop: props.alignGridTop, extraDateSpan: props.extraDateSpan, popoverContent: () => {\n                let isForcedInvisible = (props.eventDrag ? props.eventDrag.affectedInstances : null) ||\n                    (props.eventResize ? props.eventResize.affectedInstances : null) ||\n                    {};\n                return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, allSegs.map((seg) => {\n                    let instanceId = seg.eventRange.instance.instanceId;\n                    return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-daygrid-event-harness\", key: instanceId, style: {\n                            visibility: isForcedInvisible[instanceId] ? 'hidden' : '',\n                        } }, hasListItemDisplay(seg) ? ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableListItemEvent, Object.assign({ seg: seg, isDragging: false, isSelected: instanceId === props.eventSelection, defaultDisplayEventEnd: false }, (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bS)(seg, props.todayRange)))) : ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableBlockEvent, Object.assign({ seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: instanceId === props.eventSelection, defaultDisplayEventEnd: false }, (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bS)(seg, props.todayRange))))));\n                })));\n            } }));\n    }\n}\nfunction compileSegs(singlePlacements) {\n    let allSegs = [];\n    let invisibleSegs = [];\n    for (let placement of singlePlacements) {\n        allSegs.push(placement.seg);\n        if (!placement.isVisible) {\n            invisibleSegs.push(placement.seg);\n        }\n    }\n    return { allSegs, invisibleSegs };\n}\n\nconst DEFAULT_WEEK_NUM_FORMAT = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.x)({ week: 'narrow' });\nclass TableCell extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.be {\n    constructor() {\n        super(...arguments);\n        this.rootElRef = (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createRef)();\n        this.state = {\n            dayNumberId: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.a5)(),\n        };\n        this.handleRootEl = (el) => {\n            (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.Y)(this.rootElRef, el);\n            (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.Y)(this.props.elRef, el);\n        };\n    }\n    render() {\n        let { context, props, state, rootElRef } = this;\n        let { options, dateEnv } = context;\n        let { date, dateProfile } = props;\n        // TODO: memoize this?\n        const isMonthStart = props.showDayNumber &&\n            shouldDisplayMonthStart(date, dateProfile.currentRange, dateEnv);\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cl, { elTag: \"td\", elRef: this.handleRootEl, elClasses: [\n                'fc-daygrid-day',\n                ...(props.extraClassNames || []),\n            ], elAttrs: Object.assign(Object.assign(Object.assign({}, props.extraDataAttrs), (props.showDayNumber ? { 'aria-labelledby': state.dayNumberId } : {})), { role: 'gridcell' }), defaultGenerator: renderTopInner, date: date, dateProfile: dateProfile, todayRange: props.todayRange, showDayNumber: props.showDayNumber, isMonthStart: isMonthStart, extraRenderProps: props.extraRenderProps }, (InnerContent, renderProps) => ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { ref: props.innerElRef, className: \"fc-daygrid-day-frame fc-scrollgrid-sync-inner\", style: { minHeight: props.minHeight } },\n            props.showWeekNumber && ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cq, { elTag: \"a\", elClasses: ['fc-daygrid-week-number'], elAttrs: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.b0)(context, date, 'week'), date: date, defaultFormat: DEFAULT_WEEK_NUM_FORMAT })),\n            !renderProps.isDisabled &&\n                (props.showDayNumber || (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cm)(options) || props.forceDayTop) ? ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-daygrid-day-top\" },\n                (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(InnerContent, { elTag: \"a\", elClasses: [\n                        'fc-daygrid-day-number',\n                        isMonthStart && 'fc-daygrid-month-start',\n                    ], elAttrs: Object.assign(Object.assign({}, (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.b0)(context, date)), { id: state.dayNumberId }) }))) : props.showDayNumber ? (\n            // for creating correct amount of space (see issue #7162)\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-daygrid-day-top\", style: { visibility: 'hidden' } },\n                (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"a\", { className: \"fc-daygrid-day-number\" }, \"\\u00A0\"))) : undefined,\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-daygrid-day-events\", ref: props.fgContentElRef },\n                props.fgContent,\n                (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-daygrid-day-bottom\", style: { marginTop: props.moreMarginTop } },\n                    (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableCellMoreLink, { allDayDate: date, singlePlacements: props.singlePlacements, moreCnt: props.moreCnt, alignmentElRef: rootElRef, alignGridTop: !props.showDayNumber, extraDateSpan: props.extraDateSpan, dateProfile: props.dateProfile, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, todayRange: props.todayRange }))),\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: \"fc-daygrid-day-bg\" }, props.bgContent)))));\n    }\n}\nfunction renderTopInner(props) {\n    return props.dayNumberText || (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, \"\\u00A0\");\n}\nfunction shouldDisplayMonthStart(date, currentRange, dateEnv) {\n    const { start: currentStart, end: currentEnd } = currentRange;\n    const currentEndIncl = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bg)(currentEnd, -1);\n    const currentFirstYear = dateEnv.getYear(currentStart);\n    const currentFirstMonth = dateEnv.getMonth(currentStart);\n    const currentLastYear = dateEnv.getYear(currentEndIncl);\n    const currentLastMonth = dateEnv.getMonth(currentEndIncl);\n    // spans more than one month?\n    return !(currentFirstYear === currentLastYear && currentFirstMonth === currentLastMonth) &&\n        Boolean(\n        // first date in current view?\n        date.valueOf() === currentStart.valueOf() ||\n            // a month-start that's within the current range?\n            (dateEnv.getDay(date) === 1 && date.valueOf() < currentEnd.valueOf()));\n}\n\nfunction generateSegKey(seg) {\n    return seg.eventRange.instance.instanceId + ':' + seg.firstCol;\n}\nfunction generateSegUid(seg) {\n    return generateSegKey(seg) + ':' + seg.lastCol;\n}\nfunction computeFgSegPlacement(segs, // assumed already sorted\ndayMaxEvents, dayMaxEventRows, strictOrder, segHeights, maxContentHeight, cells) {\n    let hierarchy = new DayGridSegHierarchy((segEntry) => {\n        // TODO: more DRY with generateSegUid\n        let segUid = segs[segEntry.index].eventRange.instance.instanceId +\n            ':' + segEntry.span.start +\n            ':' + (segEntry.span.end - 1);\n        // if no thickness known, assume 1 (if 0, so small it always fits)\n        return segHeights[segUid] || 1;\n    });\n    hierarchy.allowReslicing = true;\n    hierarchy.strictOrder = strictOrder;\n    if (dayMaxEvents === true || dayMaxEventRows === true) {\n        hierarchy.maxCoord = maxContentHeight;\n        hierarchy.hiddenConsumes = true;\n    }\n    else if (typeof dayMaxEvents === 'number') {\n        hierarchy.maxStackCnt = dayMaxEvents;\n    }\n    else if (typeof dayMaxEventRows === 'number') {\n        hierarchy.maxStackCnt = dayMaxEventRows;\n        hierarchy.hiddenConsumes = true;\n    }\n    // create segInputs only for segs with known heights\n    let segInputs = [];\n    let unknownHeightSegs = [];\n    for (let i = 0; i < segs.length; i += 1) {\n        let seg = segs[i];\n        let segUid = generateSegUid(seg);\n        let eventHeight = segHeights[segUid];\n        if (eventHeight != null) {\n            segInputs.push({\n                index: i,\n                span: {\n                    start: seg.firstCol,\n                    end: seg.lastCol + 1,\n                },\n            });\n        }\n        else {\n            unknownHeightSegs.push(seg);\n        }\n    }\n    let hiddenEntries = hierarchy.addSegs(segInputs);\n    let segRects = hierarchy.toRects();\n    let { singleColPlacements, multiColPlacements, leftoverMargins } = placeRects(segRects, segs, cells);\n    let moreCnts = [];\n    let moreMarginTops = [];\n    // add segs with unknown heights\n    for (let seg of unknownHeightSegs) {\n        multiColPlacements[seg.firstCol].push({\n            seg,\n            isVisible: false,\n            isAbsolute: true,\n            absoluteTop: 0,\n            marginTop: 0,\n        });\n        for (let col = seg.firstCol; col <= seg.lastCol; col += 1) {\n            singleColPlacements[col].push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: false,\n                isAbsolute: false,\n                absoluteTop: 0,\n                marginTop: 0,\n            });\n        }\n    }\n    // add the hidden entries\n    for (let col = 0; col < cells.length; col += 1) {\n        moreCnts.push(0);\n    }\n    for (let hiddenEntry of hiddenEntries) {\n        let seg = segs[hiddenEntry.index];\n        let hiddenSpan = hiddenEntry.span;\n        multiColPlacements[hiddenSpan.start].push({\n            seg: resliceSeg(seg, hiddenSpan.start, hiddenSpan.end, cells),\n            isVisible: false,\n            isAbsolute: true,\n            absoluteTop: 0,\n            marginTop: 0,\n        });\n        for (let col = hiddenSpan.start; col < hiddenSpan.end; col += 1) {\n            moreCnts[col] += 1;\n            singleColPlacements[col].push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: false,\n                isAbsolute: false,\n                absoluteTop: 0,\n                marginTop: 0,\n            });\n        }\n    }\n    // deal with leftover margins\n    for (let col = 0; col < cells.length; col += 1) {\n        moreMarginTops.push(leftoverMargins[col]);\n    }\n    return { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops };\n}\n// rects ordered by top coord, then left\nfunction placeRects(allRects, segs, cells) {\n    let rectsByEachCol = groupRectsByEachCol(allRects, cells.length);\n    let singleColPlacements = [];\n    let multiColPlacements = [];\n    let leftoverMargins = [];\n    for (let col = 0; col < cells.length; col += 1) {\n        let rects = rectsByEachCol[col];\n        // compute all static segs in singlePlacements\n        let singlePlacements = [];\n        let currentHeight = 0;\n        let currentMarginTop = 0;\n        for (let rect of rects) {\n            let seg = segs[rect.index];\n            singlePlacements.push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: true,\n                isAbsolute: false,\n                absoluteTop: rect.levelCoord,\n                marginTop: rect.levelCoord - currentHeight,\n            });\n            currentHeight = rect.levelCoord + rect.thickness;\n        }\n        // compute mixed static/absolute segs in multiPlacements\n        let multiPlacements = [];\n        currentHeight = 0;\n        currentMarginTop = 0;\n        for (let rect of rects) {\n            let seg = segs[rect.index];\n            let isAbsolute = rect.span.end - rect.span.start > 1; // multi-column?\n            let isFirstCol = rect.span.start === col;\n            currentMarginTop += rect.levelCoord - currentHeight; // amount of space since bottom of previous seg\n            currentHeight = rect.levelCoord + rect.thickness; // height will now be bottom of current seg\n            if (isAbsolute) {\n                currentMarginTop += rect.thickness;\n                if (isFirstCol) {\n                    multiPlacements.push({\n                        seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n                        isVisible: true,\n                        isAbsolute: true,\n                        absoluteTop: rect.levelCoord,\n                        marginTop: 0,\n                    });\n                }\n            }\n            else if (isFirstCol) {\n                multiPlacements.push({\n                    seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n                    isVisible: true,\n                    isAbsolute: false,\n                    absoluteTop: rect.levelCoord,\n                    marginTop: currentMarginTop, // claim the margin\n                });\n                currentMarginTop = 0;\n            }\n        }\n        singleColPlacements.push(singlePlacements);\n        multiColPlacements.push(multiPlacements);\n        leftoverMargins.push(currentMarginTop);\n    }\n    return { singleColPlacements, multiColPlacements, leftoverMargins };\n}\nfunction groupRectsByEachCol(rects, colCnt) {\n    let rectsByEachCol = [];\n    for (let col = 0; col < colCnt; col += 1) {\n        rectsByEachCol.push([]);\n    }\n    for (let rect of rects) {\n        for (let col = rect.span.start; col < rect.span.end; col += 1) {\n            rectsByEachCol[col].push(rect);\n        }\n    }\n    return rectsByEachCol;\n}\nfunction resliceSeg(seg, spanStart, spanEnd, cells) {\n    if (seg.firstCol === spanStart && seg.lastCol === spanEnd - 1) {\n        return seg;\n    }\n    let eventRange = seg.eventRange;\n    let origRange = eventRange.range;\n    let slicedRange = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.o)(origRange, {\n        start: cells[spanStart].date,\n        end: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.t)(cells[spanEnd - 1].date, 1),\n    });\n    return Object.assign(Object.assign({}, seg), { firstCol: spanStart, lastCol: spanEnd - 1, eventRange: {\n            def: eventRange.def,\n            ui: Object.assign(Object.assign({}, eventRange.ui), { durationEditable: false }),\n            instance: eventRange.instance,\n            range: slicedRange,\n        }, isStart: seg.isStart && slicedRange.start.valueOf() === origRange.start.valueOf(), isEnd: seg.isEnd && slicedRange.end.valueOf() === origRange.end.valueOf() });\n}\nclass DayGridSegHierarchy extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bA {\n    constructor() {\n        super(...arguments);\n        // config\n        this.hiddenConsumes = false;\n        // allows us to keep hidden entries in the hierarchy so they take up space\n        this.forceHidden = {};\n    }\n    addSegs(segInputs) {\n        const hiddenSegs = super.addSegs(segInputs);\n        const { entriesByLevel } = this;\n        const excludeHidden = (entry) => !this.forceHidden[(0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bB)(entry)];\n        // remove the forced-hidden segs\n        for (let level = 0; level < entriesByLevel.length; level += 1) {\n            entriesByLevel[level] = entriesByLevel[level].filter(excludeHidden);\n        }\n        return hiddenSegs;\n    }\n    handleInvalidInsertion(insertion, entry, hiddenEntries) {\n        const { entriesByLevel, forceHidden } = this;\n        const { touchingEntry, touchingLevel, touchingLateral } = insertion;\n        // the entry that the new insertion is touching must be hidden\n        if (this.hiddenConsumes && touchingEntry) {\n            const touchingEntryId = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bB)(touchingEntry);\n            if (!forceHidden[touchingEntryId]) {\n                if (this.allowReslicing) {\n                    // split up the touchingEntry, reinsert it\n                    const hiddenEntry = Object.assign(Object.assign({}, touchingEntry), { span: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bF)(touchingEntry.span, entry.span) });\n                    // reinsert the area that turned into a \"more\" link (so no other entries try to\n                    // occupy the space) but mark it forced-hidden\n                    const hiddenEntryId = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bB)(hiddenEntry);\n                    forceHidden[hiddenEntryId] = true;\n                    entriesByLevel[touchingLevel][touchingLateral] = hiddenEntry;\n                    hiddenEntries.push(hiddenEntry);\n                    this.splitEntry(touchingEntry, entry, hiddenEntries);\n                }\n                else {\n                    forceHidden[touchingEntryId] = true;\n                    hiddenEntries.push(touchingEntry);\n                }\n            }\n        }\n        // will try to reslice...\n        super.handleInvalidInsertion(insertion, entry, hiddenEntries);\n    }\n}\n\nclass TableRow extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.be {\n    constructor() {\n        super(...arguments);\n        this.cellElRefs = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cf(); // the <td>\n        this.frameElRefs = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cf(); // the fc-daygrid-day-frame\n        this.fgElRefs = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cf(); // the fc-daygrid-day-events\n        this.segHarnessRefs = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cf(); // indexed by \"instanceId:firstCol\"\n        this.rootElRef = (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createRef)();\n        this.state = {\n            framePositions: null,\n            maxContentHeight: null,\n            segHeights: {},\n        };\n        this.handleResize = (isForced) => {\n            if (isForced) {\n                this.updateSizing(true); // isExternal=true\n            }\n        };\n    }\n    render() {\n        let { props, state, context } = this;\n        let { options } = context;\n        let colCnt = props.cells.length;\n        let businessHoursByCol = splitSegsByFirstCol(props.businessHourSegs, colCnt);\n        let bgEventSegsByCol = splitSegsByFirstCol(props.bgEventSegs, colCnt);\n        let highlightSegsByCol = splitSegsByFirstCol(this.getHighlightSegs(), colCnt);\n        let mirrorSegsByCol = splitSegsByFirstCol(this.getMirrorSegs(), colCnt);\n        let { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops } = computeFgSegPlacement((0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bR)(props.fgEventSegs, options.eventOrder), props.dayMaxEvents, props.dayMaxEventRows, options.eventOrderStrict, state.segHeights, state.maxContentHeight, props.cells);\n        let isForcedInvisible = // TODO: messy way to compute this\n         (props.eventDrag && props.eventDrag.affectedInstances) ||\n            (props.eventResize && props.eventResize.affectedInstances) ||\n            {};\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"tr\", { ref: this.rootElRef, role: \"row\" },\n            props.renderIntro && props.renderIntro(),\n            props.cells.map((cell, col) => {\n                let normalFgNodes = this.renderFgSegs(col, props.forPrint ? singleColPlacements[col] : multiColPlacements[col], props.todayRange, isForcedInvisible);\n                let mirrorFgNodes = this.renderFgSegs(col, buildMirrorPlacements(mirrorSegsByCol[col], multiColPlacements), props.todayRange, {}, Boolean(props.eventDrag), Boolean(props.eventResize), false);\n                return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableCell, { key: cell.key, elRef: this.cellElRefs.createRef(cell.key), innerElRef: this.frameElRefs.createRef(cell.key) /* FF <td> problem, but okay to use for left/right. TODO: rename prop */, dateProfile: props.dateProfile, date: cell.date, showDayNumber: props.showDayNumbers, showWeekNumber: props.showWeekNumbers && col === 0, forceDayTop: props.showWeekNumbers /* even displaying weeknum for row, not necessarily day */, todayRange: props.todayRange, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, extraRenderProps: cell.extraRenderProps, extraDataAttrs: cell.extraDataAttrs, extraClassNames: cell.extraClassNames, extraDateSpan: cell.extraDateSpan, moreCnt: moreCnts[col], moreMarginTop: moreMarginTops[col], singlePlacements: singleColPlacements[col], fgContentElRef: this.fgElRefs.createRef(cell.key), fgContent: ( // Fragment scopes the keys\n                    (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null,\n                        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, normalFgNodes),\n                        (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, mirrorFgNodes))), bgContent: ( // Fragment scopes the keys\n                    (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null,\n                        this.renderFillSegs(highlightSegsByCol[col], 'highlight'),\n                        this.renderFillSegs(businessHoursByCol[col], 'non-business'),\n                        this.renderFillSegs(bgEventSegsByCol[col], 'bg-event'))), minHeight: props.cellMinHeight }));\n            })));\n    }\n    componentDidMount() {\n        this.updateSizing(true);\n        this.context.addResizeHandler(this.handleResize);\n    }\n    componentDidUpdate(prevProps, prevState) {\n        let currentProps = this.props;\n        this.updateSizing(!(0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.E)(prevProps, currentProps));\n    }\n    componentWillUnmount() {\n        this.context.removeResizeHandler(this.handleResize);\n    }\n    getHighlightSegs() {\n        let { props } = this;\n        if (props.eventDrag && props.eventDrag.segs.length) { // messy check\n            return props.eventDrag.segs;\n        }\n        if (props.eventResize && props.eventResize.segs.length) { // messy check\n            return props.eventResize.segs;\n        }\n        return props.dateSelectionSegs;\n    }\n    getMirrorSegs() {\n        let { props } = this;\n        if (props.eventResize && props.eventResize.segs.length) { // messy check\n            return props.eventResize.segs;\n        }\n        return [];\n    }\n    renderFgSegs(col, segPlacements, todayRange, isForcedInvisible, isDragging, isResizing, isDateSelecting) {\n        let { context } = this;\n        let { eventSelection } = this.props;\n        let { framePositions } = this.state;\n        let defaultDisplayEventEnd = this.props.cells.length === 1; // colCnt === 1\n        let isMirror = isDragging || isResizing || isDateSelecting;\n        let nodes = [];\n        if (framePositions) {\n            for (let placement of segPlacements) {\n                let { seg } = placement;\n                let { instanceId } = seg.eventRange.instance;\n                let isVisible = placement.isVisible && !isForcedInvisible[instanceId];\n                let isAbsolute = placement.isAbsolute;\n                let left = '';\n                let right = '';\n                if (isAbsolute) {\n                    if (context.isRtl) {\n                        right = 0;\n                        left = framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol];\n                    }\n                    else {\n                        left = 0;\n                        right = framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol];\n                    }\n                }\n                /*\n                known bug: events that are force to be list-item but span multiple days still take up space in later columns\n                todo: in print view, for multi-day events, don't display title within non-start/end segs\n                */\n                nodes.push((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { className: 'fc-daygrid-event-harness' + (isAbsolute ? ' fc-daygrid-event-harness-abs' : ''), key: generateSegKey(seg), ref: isMirror ? null : this.segHarnessRefs.createRef(generateSegUid(seg)), style: {\n                        visibility: isVisible ? '' : 'hidden',\n                        marginTop: isAbsolute ? '' : placement.marginTop,\n                        top: isAbsolute ? placement.absoluteTop : '',\n                        left,\n                        right,\n                    } }, hasListItemDisplay(seg) ? ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableListItemEvent, Object.assign({ seg: seg, isDragging: isDragging, isSelected: instanceId === eventSelection, defaultDisplayEventEnd: defaultDisplayEventEnd }, (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bS)(seg, todayRange)))) : ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableBlockEvent, Object.assign({ seg: seg, isDragging: isDragging, isResizing: isResizing, isDateSelecting: isDateSelecting, isSelected: instanceId === eventSelection, defaultDisplayEventEnd: defaultDisplayEventEnd }, (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bS)(seg, todayRange))))));\n            }\n        }\n        return nodes;\n    }\n    renderFillSegs(segs, fillType) {\n        let { isRtl } = this.context;\n        let { todayRange } = this.props;\n        let { framePositions } = this.state;\n        let nodes = [];\n        if (framePositions) {\n            for (let seg of segs) {\n                let leftRightCss = isRtl ? {\n                    right: 0,\n                    left: framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol],\n                } : {\n                    left: 0,\n                    right: framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol],\n                };\n                nodes.push((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { key: (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bT)(seg.eventRange), className: \"fc-daygrid-bg-harness\", style: leftRightCss }, fillType === 'bg-event' ?\n                    (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cp, Object.assign({ seg: seg }, (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bS)(seg, todayRange))) :\n                    (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.co)(fillType)));\n            }\n        }\n        return (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, ...nodes);\n    }\n    updateSizing(isExternalSizingChange) {\n        let { props, state, frameElRefs } = this;\n        if (!props.forPrint &&\n            props.clientWidth !== null // positioning ready?\n        ) {\n            if (isExternalSizingChange) {\n                let frameEls = props.cells.map((cell) => frameElRefs.currentMap[cell.key]);\n                if (frameEls.length) {\n                    let originEl = this.rootElRef.current;\n                    let newPositionCache = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ba(originEl, frameEls, true, // isHorizontal\n                    false);\n                    if (!state.framePositions || !state.framePositions.similarTo(newPositionCache)) {\n                        this.setState({\n                            framePositions: new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ba(originEl, frameEls, true, // isHorizontal\n                            false),\n                        });\n                    }\n                }\n            }\n            const oldSegHeights = this.state.segHeights;\n            const newSegHeights = this.querySegHeights();\n            const limitByContentHeight = props.dayMaxEvents === true || props.dayMaxEventRows === true;\n            this.safeSetState({\n                // HACK to prevent oscillations of events being shown/hidden from max-event-rows\n                // Essentially, once you compute an element's height, never null-out.\n                // TODO: always display all events, as visibility:hidden?\n                segHeights: Object.assign(Object.assign({}, oldSegHeights), newSegHeights),\n                maxContentHeight: limitByContentHeight ? this.computeMaxContentHeight() : null,\n            });\n        }\n    }\n    querySegHeights() {\n        let segElMap = this.segHarnessRefs.currentMap;\n        let segHeights = {};\n        // get the max height amongst instance segs\n        for (let segUid in segElMap) {\n            let height = Math.round(segElMap[segUid].getBoundingClientRect().height);\n            segHeights[segUid] = Math.max(segHeights[segUid] || 0, height);\n        }\n        return segHeights;\n    }\n    computeMaxContentHeight() {\n        let firstKey = this.props.cells[0].key;\n        let cellEl = this.cellElRefs.currentMap[firstKey];\n        let fcContainerEl = this.fgElRefs.currentMap[firstKey];\n        return cellEl.getBoundingClientRect().bottom - fcContainerEl.getBoundingClientRect().top;\n    }\n    getCellEls() {\n        let elMap = this.cellElRefs.currentMap;\n        return this.props.cells.map((cell) => elMap[cell.key]);\n    }\n}\nTableRow.addStateEquality({\n    segHeights: _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.E,\n});\nfunction buildMirrorPlacements(mirrorSegs, colPlacements) {\n    if (!mirrorSegs.length) {\n        return [];\n    }\n    let topsByInstanceId = buildAbsoluteTopHash(colPlacements); // TODO: cache this at first render?\n    return mirrorSegs.map((seg) => ({\n        seg,\n        isVisible: true,\n        isAbsolute: true,\n        absoluteTop: topsByInstanceId[seg.eventRange.instance.instanceId],\n        marginTop: 0,\n    }));\n}\nfunction buildAbsoluteTopHash(colPlacements) {\n    let topsByInstanceId = {};\n    for (let placements of colPlacements) {\n        for (let placement of placements) {\n            topsByInstanceId[placement.seg.eventRange.instance.instanceId] = placement.absoluteTop;\n        }\n    }\n    return topsByInstanceId;\n}\n\nclass TableRows extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.be {\n    constructor() {\n        super(...arguments);\n        this.splitBusinessHourSegs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(splitSegsByRow);\n        this.splitBgEventSegs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(splitSegsByRow);\n        this.splitFgEventSegs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(splitSegsByRow);\n        this.splitDateSelectionSegs = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(splitSegsByRow);\n        this.splitEventDrag = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(splitInteractionByRow);\n        this.splitEventResize = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(splitInteractionByRow);\n        this.rowRefs = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cf();\n    }\n    render() {\n        let { props, context } = this;\n        let rowCnt = props.cells.length;\n        let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, rowCnt);\n        let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, rowCnt);\n        let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, rowCnt);\n        let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, rowCnt);\n        let eventDragByRow = this.splitEventDrag(props.eventDrag, rowCnt);\n        let eventResizeByRow = this.splitEventResize(props.eventResize, rowCnt);\n        // for DayGrid view with many rows, force a min-height on cells so doesn't appear squished\n        // choose 7 because a month view will have max 6 rows\n        let cellMinHeight = (rowCnt >= 7 && props.clientWidth) ?\n            props.clientWidth / context.options.aspectRatio / 6 :\n            null;\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ch, { unit: \"day\" }, (nowDate, todayRange) => ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, props.cells.map((cells, row) => ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableRow, { ref: this.rowRefs.createRef(row), key: cells.length\n                ? cells[0].date.toISOString() /* best? or put key on cell? or use diff formatter? */\n                : row // in case there are no cells (like when resource view is loading)\n            , showDayNumbers: rowCnt > 1, showWeekNumbers: props.showWeekNumbers, todayRange: todayRange, dateProfile: props.dateProfile, cells: cells, renderIntro: props.renderRowIntro, businessHourSegs: businessHourSegsByRow[row], eventSelection: props.eventSelection, bgEventSegs: bgEventSegsByRow[row].filter(isSegAllDay) /* hack */, fgEventSegs: fgEventSegsByRow[row], dateSelectionSegs: dateSelectionSegsByRow[row], eventDrag: eventDragByRow[row], eventResize: eventResizeByRow[row], dayMaxEvents: props.dayMaxEvents, dayMaxEventRows: props.dayMaxEventRows, clientWidth: props.clientWidth, clientHeight: props.clientHeight, cellMinHeight: cellMinHeight, forPrint: props.forPrint })))))));\n    }\n    componentDidMount() {\n        this.registerInteractiveComponent();\n    }\n    componentDidUpdate() {\n        // for if started with zero cells\n        this.registerInteractiveComponent();\n    }\n    registerInteractiveComponent() {\n        if (!this.rootEl) {\n            // HACK: need a daygrid wrapper parent to do positioning\n            // NOTE: a daygrid resource view w/o resources can have zero cells\n            const firstCellEl = this.rowRefs.currentMap[0].getCellEls()[0];\n            const rootEl = firstCellEl ? firstCellEl.closest('.fc-daygrid-body') : null;\n            if (rootEl) {\n                this.rootEl = rootEl;\n                this.context.registerInteractiveComponent(this, {\n                    el: rootEl,\n                    isHitComboAllowed: this.props.isHitComboAllowed,\n                });\n            }\n        }\n    }\n    componentWillUnmount() {\n        if (this.rootEl) {\n            this.context.unregisterInteractiveComponent(this);\n            this.rootEl = null;\n        }\n    }\n    // Hit System\n    // ----------------------------------------------------------------------------------------------------\n    prepareHits() {\n        this.rowPositions = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ba(this.rootEl, this.rowRefs.collect().map((rowObj) => rowObj.getCellEls()[0]), // first cell el in each row. TODO: not optimal\n        false, true);\n        this.colPositions = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.ba(this.rootEl, this.rowRefs.currentMap[0].getCellEls(), // cell els in first row\n        true, // horizontal\n        false);\n    }\n    queryHit(positionLeft, positionTop) {\n        let { colPositions, rowPositions } = this;\n        let col = colPositions.leftToIndex(positionLeft);\n        let row = rowPositions.topToIndex(positionTop);\n        if (row != null && col != null) {\n            let cell = this.props.cells[row][col];\n            return {\n                dateProfile: this.props.dateProfile,\n                dateSpan: Object.assign({ range: this.getCellRange(row, col), allDay: true }, cell.extraDateSpan),\n                dayEl: this.getCellEl(row, col),\n                rect: {\n                    left: colPositions.lefts[col],\n                    right: colPositions.rights[col],\n                    top: rowPositions.tops[row],\n                    bottom: rowPositions.bottoms[row],\n                },\n                layer: 0,\n            };\n        }\n        return null;\n    }\n    getCellEl(row, col) {\n        return this.rowRefs.currentMap[row].getCellEls()[col]; // TODO: not optimal\n    }\n    getCellRange(row, col) {\n        let start = this.props.cells[row][col].date;\n        let end = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.t)(start, 1);\n        return { start, end };\n    }\n}\nfunction isSegAllDay(seg) {\n    return seg.eventRange.def.allDay;\n}\n\nclass Table extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.be {\n    constructor() {\n        super(...arguments);\n        this.elRef = (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createRef)();\n        this.needsScrollReset = false;\n    }\n    render() {\n        let { props } = this;\n        let { dayMaxEventRows, dayMaxEvents, expandRows } = props;\n        let limitViaBalanced = dayMaxEvents === true || dayMaxEventRows === true;\n        // if rows can't expand to fill fixed height, can't do balanced-height event limit\n        // TODO: best place to normalize these options?\n        if (limitViaBalanced && !expandRows) {\n            limitViaBalanced = false;\n            dayMaxEventRows = null;\n            dayMaxEvents = null;\n        }\n        let classNames = [\n            'fc-daygrid-body',\n            limitViaBalanced ? 'fc-daygrid-body-balanced' : 'fc-daygrid-body-unbalanced',\n            expandRows ? '' : 'fc-daygrid-body-natural', // will height of one row depend on the others?\n        ];\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", { ref: this.elRef, className: classNames.join(' '), style: {\n                // these props are important to give this wrapper correct dimensions for interactions\n                // TODO: if we set it here, can we avoid giving to inner tables?\n                width: props.clientWidth,\n                minWidth: props.tableMinWidth,\n            } },\n            (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"table\", { role: \"presentation\", className: \"fc-scrollgrid-sync-table\", style: {\n                    width: props.clientWidth,\n                    minWidth: props.tableMinWidth,\n                    height: expandRows ? props.clientHeight : '',\n                } },\n                props.colGroupNode,\n                (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"tbody\", { role: \"presentation\" },\n                    (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(TableRows, { dateProfile: props.dateProfile, cells: props.cells, renderRowIntro: props.renderRowIntro, showWeekNumbers: props.showWeekNumbers, clientWidth: props.clientWidth, clientHeight: props.clientHeight, businessHourSegs: props.businessHourSegs, bgEventSegs: props.bgEventSegs, fgEventSegs: props.fgEventSegs, dateSelectionSegs: props.dateSelectionSegs, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, dayMaxEvents: dayMaxEvents, dayMaxEventRows: dayMaxEventRows, forPrint: props.forPrint, isHitComboAllowed: props.isHitComboAllowed })))));\n    }\n    componentDidMount() {\n        this.requestScrollReset();\n    }\n    componentDidUpdate(prevProps) {\n        if (prevProps.dateProfile !== this.props.dateProfile) {\n            this.requestScrollReset();\n        }\n        else {\n            this.flushScrollReset();\n        }\n    }\n    requestScrollReset() {\n        this.needsScrollReset = true;\n        this.flushScrollReset();\n    }\n    flushScrollReset() {\n        if (this.needsScrollReset &&\n            this.props.clientWidth // sizes computed?\n        ) {\n            const subjectEl = getScrollSubjectEl(this.elRef.current, this.props.dateProfile);\n            if (subjectEl) {\n                const originEl = subjectEl.closest('.fc-daygrid-body');\n                const scrollEl = originEl.closest('.fc-scroller');\n                const scrollTop = subjectEl.getBoundingClientRect().top -\n                    originEl.getBoundingClientRect().top;\n                scrollEl.scrollTop = scrollTop ? (scrollTop + 1) : 0; // overcome border\n            }\n            this.needsScrollReset = false;\n        }\n    }\n}\nfunction getScrollSubjectEl(containerEl, dateProfile) {\n    let el;\n    if (dateProfile.currentRangeUnit.match(/year|month/)) {\n        el = containerEl.querySelector(`[data-date=\"${(0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bx)(dateProfile.currentDate)}-01\"]`);\n        // even if view is month-based, first-of-month might be hidden...\n    }\n    if (!el) {\n        el = containerEl.querySelector(`[data-date=\"${(0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bv)(dateProfile.currentDate)}\"]`);\n        // could still be hidden if an interior-view hidden day\n    }\n    return el;\n}\n\nclass DayTableSlicer extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bW {\n    constructor() {\n        super(...arguments);\n        this.forceDayIfListItem = true;\n    }\n    sliceRange(dateRange, dayTableModel) {\n        return dayTableModel.sliceRange(dateRange);\n    }\n}\n\nclass DayTable extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.be {\n    constructor() {\n        super(...arguments);\n        this.slicer = new DayTableSlicer();\n        this.tableRef = (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createRef)();\n    }\n    render() {\n        let { props, context } = this;\n        return ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(Table, Object.assign({ ref: this.tableRef }, this.slicer.sliceProps(props, props.dateProfile, props.nextDayThreshold, context, props.dayTableModel), { dateProfile: props.dateProfile, cells: props.dayTableModel.cells, colGroupNode: props.colGroupNode, tableMinWidth: props.tableMinWidth, renderRowIntro: props.renderRowIntro, dayMaxEvents: props.dayMaxEvents, dayMaxEventRows: props.dayMaxEventRows, showWeekNumbers: props.showWeekNumbers, expandRows: props.expandRows, headerAlignElRef: props.headerAlignElRef, clientWidth: props.clientWidth, clientHeight: props.clientHeight, forPrint: props.forPrint })));\n    }\n}\n\nclass DayTableView extends TableView {\n    constructor() {\n        super(...arguments);\n        this.buildDayTableModel = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.z)(buildDayTableModel);\n        this.headerRef = (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createRef)();\n        this.tableRef = (0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createRef)();\n        // can't override any lifecycle methods from parent\n    }\n    render() {\n        let { options, dateProfileGenerator } = this.context;\n        let { props } = this;\n        let dayTableModel = this.buildDayTableModel(props.dateProfile, dateProfileGenerator);\n        let headerContent = options.dayHeaders && ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bK, { ref: this.headerRef, dateProfile: props.dateProfile, dates: dayTableModel.headerDates, datesRepDistinctDays: dayTableModel.rowCnt === 1 }));\n        let bodyContent = (contentArg) => ((0,_fullcalendar_core_preact_js__WEBPACK_IMPORTED_MODULE_1__.createElement)(DayTable, { ref: this.tableRef, dateProfile: props.dateProfile, dayTableModel: dayTableModel, businessHours: props.businessHours, dateSelection: props.dateSelection, eventStore: props.eventStore, eventUiBases: props.eventUiBases, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, nextDayThreshold: options.nextDayThreshold, colGroupNode: contentArg.tableColGroupNode, tableMinWidth: contentArg.tableMinWidth, dayMaxEvents: options.dayMaxEvents, dayMaxEventRows: options.dayMaxEventRows, showWeekNumbers: options.weekNumbers, expandRows: !props.isHeightAuto, headerAlignElRef: this.headerElRef, clientWidth: contentArg.clientWidth, clientHeight: contentArg.clientHeight, forPrint: props.forPrint }));\n        return options.dayMinWidth\n            ? this.renderHScrollLayout(headerContent, bodyContent, dayTableModel.colCnt, options.dayMinWidth)\n            : this.renderSimpleLayout(headerContent, bodyContent);\n    }\n}\nfunction buildDayTableModel(dateProfile, dateProfileGenerator) {\n    let daySeries = new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bO(dateProfile.renderRange, dateProfileGenerator);\n    return new _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bV(daySeries, /year|month|week/.test(dateProfile.currentRangeUnit));\n}\n\nclass TableDateProfileGenerator extends _fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.U {\n    // Computes the date range that will be rendered\n    buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay) {\n        let renderRange = super.buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay);\n        let { props } = this;\n        return buildDayTableRenderRange({\n            currentRange: renderRange,\n            snapToWeek: /^(year|month)$/.test(currentRangeUnit),\n            fixedWeekCount: props.fixedWeekCount,\n            dateEnv: props.dateEnv,\n        });\n    }\n}\nfunction buildDayTableRenderRange(props) {\n    let { dateEnv, currentRange } = props;\n    let { start, end } = currentRange;\n    let endOfWeek;\n    // year and month views should be aligned with weeks. this is already done for week\n    if (props.snapToWeek) {\n        start = dateEnv.startOfWeek(start);\n        // make end-of-week if not already\n        endOfWeek = dateEnv.startOfWeek(end);\n        if (endOfWeek.valueOf() !== end.valueOf()) {\n            end = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bh)(endOfWeek, 1);\n        }\n    }\n    // ensure 6 weeks\n    if (props.fixedWeekCount) {\n        // TODO: instead of these date-math gymnastics (for multimonth view),\n        // compute dateprofiles of all months, then use start of first and end of last.\n        let lastMonthRenderStart = dateEnv.startOfWeek(dateEnv.startOfMonth((0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.t)(currentRange.end, -1)));\n        let rowCnt = Math.ceil(// could be partial weeks due to hiddenDays\n        (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bi)(lastMonthRenderStart, end));\n        end = (0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.bh)(end, 6 - rowCnt);\n    }\n    return { start, end };\n}\n\nvar css_248z = \":root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}\";\n(0,_fullcalendar_core_internal_js__WEBPACK_IMPORTED_MODULE_0__.cw)(css_248z);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@fullcalendar+daygrid@6.1.15_@fullcalendar+core@6.1.15/node_modules/@fullcalendar/daygrid/internal.js\n");

/***/ })

};
;